{"name": "patreon-exporter", "description": "Export Patreon posts to clean PDFs for offline reading", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "jspdf": "^2.5.2", "html2canvas": "^1.4.1"}, "devDependencies": {"@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@wxt-dev/module-react": "^1.1.3", "typescript": "^5.8.3", "wxt": "^0.20.6"}}